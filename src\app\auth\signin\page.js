"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

// Extract the SignIn component from your Signin.js file
const SignIn = () => {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  // State to manage focus for each input field for floating labels
  const [isEmailFocused, setIsEmailFocused] = useState(false);
  const [isUsernameFocused, setIsUsernameFocused] = useState(false);
  const [isPasswordFocused, setIsPasswordFocused] = useState(false);

  // Handle email/username/password form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');
    console.log('Sign In attempted with:', { email, username, password });
    if (email && password) {
      console.log("Simulating traditional sign-in success. Redirecting to home.");
      // Redirect to home page after successful sign-in
      router.push('/');
    } else {
      setError("Please fill in email and password.");
    }
  };

  // Handle Google Sign-In
  const handleGoogleSignIn = () => {
    setError('');
    console.log('Google Sign-In attempted. Redirecting to home.');
    // Redirect to home page after Google sign-in
    router.push('/');
  };

  // Function to determine if a label should float
  const shouldLabelFloat = (isFocused, value) => isFocused || value.length > 0;

  // Base styles for the input fields
  const inputBaseStyle = {
    padding: "20px 15px 10px 15px",
    fontSize: "1em",
    borderRadius: "8px",
    border: "1px solid #30363d",
    backgroundColor: "#0d1117",
    color: "#f0f6fc",
    outline: "none",
    boxShadow: "inset 0 1px 3px rgba(0, 0, 0, 0.2)",
    transition: "border-color 0.2s ease, box-shadow 0.2s ease",
    width: "100%",
    boxSizing: "border-box",
  };

  // Styles for input field when focused
  const inputFocusStyle = {
    borderColor: "#58a6ff",
    boxShadow: "inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 3px rgba(88, 166, 255, 0.4)",
  };

  // Base styles for the label
  const labelBaseStyle = {
    position: "absolute",
    left: "15px",
    top: "15px",
    color: "#8b949e",
    fontSize: "1em",
    pointerEvents: "none",
    transition: "top 0.2s ease, font-size 0.2s ease, color 0.2s ease, background-color 0.2s ease",
    zIndex: 1,
  };

  // Styles for the label when it floats above the input field
  const labelFloatStyle = {
    top: "-10px",
    fontSize: "0.8em",
    color: "#58a6ff",
    backgroundColor: "#161b22",
    padding: "0 5px",
    left: "10px",
  };

  // Button Styles
  const buttonBaseStyle = {
    padding: '15px 30px',
    fontSize: '1.1em',
    fontWeight: 'bold',
    cursor: 'pointer',
    border: 'none',
    borderRadius: '8px',
    backgroundColor: '#2ea44f',
    color: '#ffffff',
    marginTop: '10px',
    transition: 'background-color 0.2s ease, transform 0.2s ease-out, box-shadow 0.2s ease-out',
    boxShadow: '0 4px 15px rgba(46, 164, 79, 0.3)',
  };

  // Hover styles for the button
  const buttonHoverStyle = {
    backgroundColor: '#2c974b',
    transform: 'scale(1.02)',
    boxShadow: '0 6px 20px rgba(46, 164, 79, 0.4)',
  };

  // Click (active) styles for the button
  const buttonActiveStyle = {
    transform: 'scale(0.98)',
    boxShadow: '0 2px 8px rgba(46, 164, 79, 0.2)',
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#0d1117',
      fontFamily: "'Inter', sans-serif",
      color: '#c9d1d9',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '1.25rem',
      boxSizing: 'border-box'
    }}>
      <style jsx>{`
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
        body {
          font-family: 'Inter', sans-serif;
        }
        @media (min-width: 640px) {
          .form-container {
            max-width: 28rem;
          }
        }
        @media (min-width: 768px) {
          .form-container {
            max-width: 32rem;
            padding: 2.5rem;
          }
          .heading {
            font-size: 3.75rem;
          }
        }
      `}</style>

      <h1 style={{
        fontSize: '2.8em',
        marginBottom: '30px',
        color: '#ffffff',
        fontWeight: '600',
        letterSpacing: '-0.5px',
        textAlign: 'center',
      }}>
        Welcome Back!
      </h1>

      <form
        onSubmit={handleSubmit}
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '25px',
          padding: '40px',
          maxWidth: '450px',
          width: '100%',
          backgroundColor: '#161b22',
          borderRadius: '12px',
          boxShadow: '0 8px 30px rgba(0, 0, 0, 0.4)',
          border: '1px solid #30363d',
        }}
        className="form-container"
      >
        {/* Error message display */}
        {error && (
          <div style={{
            backgroundColor: '#7f1d1d',
            color: '#fca5a5',
            padding: '0.75rem',
            borderRadius: '0.375rem',
            fontSize: '0.875rem',
            textAlign: 'center'
          }}>
            {error}
          </div>
        )}

        {/* Email Input Field Group */}
        <div style={{ position: 'relative', width: '100%' }}>
          <label
            htmlFor="email"
            style={{
              ...labelBaseStyle,
              ...(shouldLabelFloat(isEmailFocused, email) ? labelFloatStyle : {}),
            }}
          >
            Email Address
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            onFocus={() => setIsEmailFocused(true)}
            onBlur={() => setIsEmailFocused(false)}
            style={{
              ...inputBaseStyle,
              ...(isEmailFocused ? inputFocusStyle : {}),
            }}
          />
        </div>

        {/* Username Input Field Group */}
        <div style={{ position: 'relative', width: '100%' }}>
          <label
            htmlFor="username"
            style={{
              ...labelBaseStyle,
              ...(shouldLabelFloat(isUsernameFocused, username) ? labelFloatStyle : {}),
            }}
          >
            Username
          </label>
          <input
            id="username"
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            onFocus={() => setIsUsernameFocused(true)}
            onBlur={() => setIsUsernameFocused(false)}
            style={{
              ...inputBaseStyle,
              ...(isUsernameFocused ? inputFocusStyle : {}),
            }}
          />
        </div>

        {/* Password Input Field Group */}
        <div style={{ position: 'relative', width: '100%' }}>
          <label
            htmlFor="password"
            style={{
              ...labelBaseStyle,
              ...(shouldLabelFloat(isPasswordFocused, password) ? labelFloatStyle : {}),
            }}
          >
            Password
          </label>
          <input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            onFocus={() => setIsPasswordFocused(true)}
            onBlur={() => setIsPasswordFocused(false)}
            style={{
              ...inputBaseStyle,
              ...(isPasswordFocused ? inputFocusStyle : {}),
            }}
          />
        </div>

        {/* Traditional Sign In Button */}
        <button
          type="submit"
          style={buttonBaseStyle}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, buttonHoverStyle)}
          onMouseLeave={(e) => Object.assign(e.currentTarget.style, buttonBaseStyle)}
          onMouseDown={(e) => Object.assign(e.currentTarget.style, buttonActiveStyle)}
          onMouseUp={(e) => Object.assign(e.currentTarget.style, buttonHoverStyle)}
        >
          Sign In
        </button>

        {/* Or Divider */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          margin: '1rem 0',
        }}>
          <div style={{ flexGrow: 1, borderTop: '1px solid #4a5568' }}></div>
          <span style={{ flexShrink: 0, margin: '0 1rem', color: '#a0aec0' }}>OR</span>
          <div style={{ flexGrow: 1, borderTop: '1px solid #4a5568' }}></div>
        </div>

        {/* Google OAuth Button */}
        <button
          type="button"
          onClick={handleGoogleSignIn}
          style={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.75rem',
            padding: '1rem 1.5rem',
            fontSize: '1.125rem',
            fontWeight: 'bold',
            cursor: 'pointer',
            borderRadius: '0.5rem',
            backgroundColor: '#4285f4',
            color: '#ffffff',
            border: 'none',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            transition: 'background-color 0.2s ease, transform 0.2s ease',
          }}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#3367d6'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#4285f4'}
          onMouseDown={(e) => e.currentTarget.style.transform = 'scale(0.98)'}
          onMouseUp={(e) => e.currentTarget.style.transform = 'scale(1)'}
        >
          Sign in with Google
        </button>

        {/* Link to Create Account */}
        <div style={{
          marginTop: '20px',
          fontSize: '0.95em',
          color: '#8b949e',
          textAlign: 'center',
        }}>
          New to our platform?{" "}
          <button
            type="button"
            onClick={() => router.push('/auth/signup')}
            style={{
              color: '#58a6ff',
              textDecoration: 'none',
              fontWeight: 'bold',
              transition: 'color 0.2s ease',
              background: 'none',
              border: 'none',
              padding: 0,
              cursor: 'pointer',
            }}
            onMouseEnter={(e) => (e.currentTarget.style.color = '#79c0ff')}
            onMouseLeave={(e) => (e.currentTarget.style.color = '#58a6ff')}
          >
            Create an account
          </button>
        </div>
      </form>
    </div>
  );
};

export default function SigninPage() {
  return <SignIn />;
}
