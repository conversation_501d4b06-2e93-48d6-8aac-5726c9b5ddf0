# Next.js Routing Guide for Luxentra Project

## Project Structure Overview

Your project uses **Next.js 15 with App Router** which means:
- Pages are created in the `src/app/` directory
- Each route needs a `page.js` file
- Layouts are handled by `layout.js` files
- Components go in `src/components/`

## Current Setup

```
src/
├── app/
│   ├── layout.js          # Root layout (includes Nav<PERSON> & Footer)
│   ├── page.js            # Home page (/)
│   ├── globals.css        # Global styles
│   └── auth/
│       └── signin/
│           └── page.js    # Sign-in page (/auth/signin)
├── components/
│   ├── auth/
│   │   ├── Signin.js      # Your original signin component
│   │   └── Signup.js      # Your signup component
│   └── layout/
│       ├── Navbar.js      # Navigation component
│       └── Footer.js      # Footer component
└── lib/
    └── utils.js           # Utility functions
```

## How to Add New Pages

### 1. Simple Page (e.g., About Page)

**Step 1:** Create the directory structure
```
src/app/about/page.js
```

**Step 2:** Create the page component
```javascript
// src/app/about/page.js
export default function AboutPage() {
  return (
    <div className="min-h-screen pt-24 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8">About Luxentra</h1>
        <p>Your about content here...</p>
      </div>
    </div>
  );
}
```

**Result:** Accessible at `/about`

### 2. Nested Routes (e.g., Products)

**Step 1:** Create nested structure
```
src/app/products/
├── page.js              # /products
├── [id]/
│   └── page.js          # /products/[id]
└── category/
    └── [slug]/
        └── page.js      # /products/category/[slug]
```

**Step 2:** Create the main products page
```javascript
// src/app/products/page.js
export default function ProductsPage() {
  return (
    <div className="min-h-screen pt-24">
      <h1>All Products</h1>
      {/* Product listing */}
    </div>
  );
}
```

**Step 3:** Create dynamic product page
```javascript
// src/app/products/[id]/page.js
export default function ProductPage({ params }) {
  const { id } = params;
  
  return (
    <div className="min-h-screen pt-24">
      <h1>Product {id}</h1>
      {/* Product details */}
    </div>
  );
}
```

### 3. Using Existing Components

**If you have an existing component** (like your Signin.js):

**Option A:** Extract the component logic (Recommended)
```javascript
// src/app/auth/signup/page.js
"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function SignupPage() {
  const router = useRouter();
  // Extract your component logic here
  // Remove any internal routing logic
  // Use router.push() for navigation
  
  return (
    // Your component JSX
  );
}
```

**Option B:** Wrap existing component
```javascript
// src/app/auth/signup/page.js
import SignupComponent from '@/components/auth/Signup';

export default function SignupPage() {
  return <SignupComponent />;
}
```

## Navigation Updates

### Update Navbar Links

When adding new pages, update your navbar:

```javascript
// src/components/layout/Navbar.js
const navigation = [
  { name: "Products", href: "/products" },
  { name: "Collections", href: "/collections" },
  { name: "About", href: "/about" },
  { name: "Support", href: "/support" },
  { name: "Sign In", href: "/auth/signin" },
];
```

### Programmatic Navigation

In your components, use Next.js router:

```javascript
"use client";
import { useRouter } from 'next/navigation';

export default function MyComponent() {
  const router = useRouter();
  
  const handleClick = () => {
    router.push('/products');
  };
  
  return <button onClick={handleClick}>Go to Products</button>;
}
```

## Important Notes

### 1. Client vs Server Components
- Use `"use client"` directive for components that need:
  - useState, useEffect, event handlers
  - Browser APIs
  - Interactive features

### 2. Path Aliases
Your project is configured with `@/*` alias:
```javascript
// Instead of: import Navbar from '../../../components/layout/Navbar'
import Navbar from '@/components/layout/Navbar';
```

### 3. Layout Inheritance
- Your root `layout.js` includes Navbar and Footer
- All pages automatically get this layout
- Create custom layouts for specific sections if needed

### 4. Styling
- Global styles in `src/app/globals.css`
- Tailwind CSS is configured
- Add `pt-24` class to account for fixed navbar

## Common Patterns

### 1. Protected Routes
```javascript
// src/app/dashboard/page.js
"use client";
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const router = useRouter();
  
  useEffect(() => {
    // Check authentication
    const isAuthenticated = false; // Your auth logic
    if (!isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [router]);
  
  return <div>Dashboard Content</div>;
}
```

### 2. Loading States
```javascript
// src/app/products/loading.js
export default function Loading() {
  return <div>Loading products...</div>;
}
```

### 3. Error Handling
```javascript
// src/app/products/error.js
"use client";

export default function Error({ error, reset }) {
  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={() => reset()}>Try again</button>
    </div>
  );
}
```

## Quick Checklist for New Pages

- [ ] Create `page.js` in appropriate directory under `src/app/`
- [ ] Add `"use client"` if using interactive features
- [ ] Update navigation links in Navbar.js
- [ ] Add proper styling (include `pt-24` for navbar spacing)
- [ ] Test the route in browser
- [ ] Update any internal links to use the new route

## Development Server

Always run the development server to test your routes:
```bash
npm run dev
```

Your app will be available at `http://localhost:3000` (or next available port).
